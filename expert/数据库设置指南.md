# 数据库设置指南

## 📋 前置要求

1. **安装MySQL 8.0+**
   - 下载地址：https://dev.mysql.com/downloads/mysql/
   - 或使用包管理器安装（如 `brew install mysql` 或 `apt install mysql-server`）

2. **启动MySQL服务**
   ```bash
   # Windows
   net start mysql
   
   # macOS
   brew services start mysql
   
   # Linux
   sudo systemctl start mysql
   ```

## 🗄️ 数据库创建步骤

### 步骤1：连接MySQL
```bash
mysql -u root -p
```

### 步骤2：创建数据库
```sql
CREATE DATABASE expert_db CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
```

### 步骤3：创建用户（可选）
```sql
-- 创建专用用户
CREATE USER 'expert_user'@'localhost' IDENTIFIED BY 'expert_password';

-- 授权
GRANT ALL PRIVILEGES ON expert_db.* TO 'expert_user'@'localhost';
FLUSH PRIVILEGES;
```

### 步骤4：导入数据库结构
```bash
# 在项目根目录执行
mysql -u root -p expert_db < expert/src/main/resources/sql/expert_db.sql
```

### 步骤5：导入初始数据（可选）
```bash
mysql -u root -p expert_db < expert/src/main/resources/sql/expert_db_data.sql
```

## ⚙️ 配置文件设置

### 开发环境（默认配置）
当前配置使用：
- 主机：localhost
- 端口：3306
- 数据库：expert_db
- 用户名：root
- 密码：空（无密码）

### 自定义配置
如果你的MySQL配置不同，可以通过环境变量设置：

```bash
# Windows
set DB_HOST=localhost
set DB_PORT=3306
set DB_NAME=expert_db
set DB_USERNAME=root
set DB_PASSWORD=your_password

# macOS/Linux
export DB_HOST=localhost
export DB_PORT=3306
export DB_NAME=expert_db
export DB_USERNAME=root
export DB_PASSWORD=your_password
```

## 🔧 常见问题解决

### 问题1：连接被拒绝
```
Failed to obtain JDBC Connection
```
**解决方案**：
1. 确认MySQL服务已启动
2. 检查端口3306是否被占用
3. 验证用户名密码是否正确

### 问题2：数据库不存在
```
Unknown database 'expert_db'
```
**解决方案**：
1. 执行步骤2创建数据库
2. 确认数据库名称拼写正确

### 问题3：权限不足
```
Access denied for user
```
**解决方案**：
1. 检查用户名密码
2. 确认用户有数据库访问权限
3. 可以使用root用户进行测试

## 🚀 验证数据库连接

运行应用程序验证连接：
```bash
cd expert
mvn spring-boot:run -Dspring-boot.run.arguments="--spring.profiles.active=dev"
```

看到以下日志表示连接成功：
```
✅ 数据库配置验证通过
✅ 应用配置验证完成
🚀 🎉 应用已完全启动并准备就绪！
```

## 📞 需要帮助？

如果遇到问题，请提供以下信息：
1. 操作系统版本
2. MySQL版本
3. 错误日志
4. 当前配置信息
